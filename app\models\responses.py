from pydantic import BaseModel, <PERSON>, validator
from typing import Optional, Dict, Any, List
from enum import Enum
from decimal import Decimal

class ResponseType(str, Enum):
    """Types of responses the agent can return"""
    INFORMATION = "information"
    SEARCH_FILTERS = "search_filters"
    GREETING = "greeting"
    VEHICLE_SEARCH_OFFER = "vehicle_search_offer"
    ERROR = "error"

class VehicleInventoryItem(BaseModel):
    row_id: int
    body_type: Optional[str]
    exterior_color: Optional[str]
    fp_dealer_id: Optional[int]
    fuel_type: Optional[str]
    heading: Optional[str]
    high_value_features: Optional[str]
    interior_color: Optional[str]
    inventory_type: Optional[str]
    latitude: Optional[Decimal]
    longitude: Optional[Decimal]
    make: Optional[str]
    miles: Optional[int]
    model: Optional[str]
    photo_url: Optional[str]
    price: Optional[int]
    stock_no: Optional[str]
    trim: Optional[str]
    vin: Optional[str]
    year: Optional[int]

    class Config:
        orm_mode = True

class VehicleFilters(BaseModel):
    ...
    @validator("*", pre=True)
    def empty_str_to_none(cls, v):
        if isinstance(v, str) and v.strip() == "":
            return None
        return v
    
class VehicleQueryResponse(BaseModel):
    """Response model for vehicle queries"""
    success: bool = Field(True, description="Whether the query was successful")
    response_type: ResponseType = Field(ResponseType.INFORMATION, description="Type of response")
    message: str = Field('', description="Human-readable response message")
    html_content: Optional[str] = Field(None, description="HTML formatted content for display")
    filters: Optional[Dict[str, Any]] = Field(None, description="Search filters if applicable")
    metadata: Optional[Dict[str, Any]] = Field(None, description="Additional metadata")
    vehicles: List[VehicleInventoryItem] = Field(
        default_factory=list,
        description="List of matching inventory items"
    )
    total_vehicle_count: int = Field(
        0,
        description="Total count of vehicles matching the search criteria"
    )
    class Config:
        json_schema_extra = {
            "example": {
                "success": True,
                "response_type": "information",
                "message": "Here's information about the Audi A4 2022",
                "html_content": "<div><h3>Audi A4 2022</h3><p>The 2022 Audi A4...</p></div>",
                "filters": None,
                "metadata": {"sources": ["web_search"]},
                "vehicles": [
                    {
                        "row_id": 7522209,
                        "body_type": "SUV",
                        "exterior_color": "Nh-912px Urban Gra",
                        "fp_dealer_id": 7954,
                        "fuel_type": None,
                        "heading": "2025 Acura",
                        "high_value_features": None,
                        "interior_color": "Nh-912px/Red",
                        "inventory_type": "new",
                        "latitude": 36.050831,
                        "longitude": -115.026314,
                        "make": "Acura",
                        "miles": None,
                        "model": "ADX",
                        "photo_url": "https://images.dealer.com/unavailable_stockphoto.png",
                        "price": 39950,
                        "stock_no": "40908-20",
                        "trim": None,
                        "vin": "3HDSA1H52SM701682",
                        "year": 2025
                    }
                ]
            }
        }

