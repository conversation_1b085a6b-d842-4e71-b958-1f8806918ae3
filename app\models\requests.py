from pydantic import BaseModel, Field
from typing import Optional, List, Dict, Any

class VehicleQueryRequest(BaseModel):
    """Request model for vehicle queries"""
    query: str = Field(..., description="User's vehicle-related question", min_length=1, max_length=1000)
    conversation_id: Optional[str] = Field(None, description="ID of the conversation")
    lat: float = Field(..., description='Latitude for the User Location')
    long: float = Field(..., description='Longitude for the User Location')
    page: int = Field(1, description="Current Page of the inventory")
    page_size: int = Field(20, description="Total vehicle details fetched per page")
    sort_by: str = Field('price', description="Field to sort by")
    sort_order: str = Field('asc', description="Sort order (asc/desc)")
    filters: Optional[Dict[str, Any]] = Field(None, description="Optional filters for vehicle search")
    zipcode: str = Field(..., description="User zipcode")
    user_name: str = Field(..., description="User name")
    user_purchase_power_amount: float = Field(..., description="User approved amount")
    user_loan_approval_amount: float = Field(..., description = "User loan approved amount")
    addedCarinGarageVins: Optional[List[str]] = Field(None, description="List of VIN numbers to exclude from search results")
    class Config:
        json_schema_extra = {
            "example": {
                "query": "Show me Audi A4 2022 models under $50,000",
                "conversation_id": "abcde-67890",
                "lat": 34.01,
                "long": -118.41,
                "zipcode":"965783",
                "user_name":"Soko Moto",
                "user_purchase_power_amount":456.87,
                "user_loan_approval_amount":60000,
                "page": 1,
                "page_size": 20,
                "sort_by": "price",
                "sort_order": "asc",
                "filters": {
                    "make": "Audi",
                    "model": "A4",
                    "year_min": 2023,
                    "year_max": 2023,
                    "price_min": None,
                    "price_max": None,
                    "mileage_max": None,
                    "fuel_type": None,
                    "transmission": None,
                    "body_type": None,
                    "condition": None
                },
                "addedCarinGarageVins": ["1HGBH41JXMN109186", "2HGBH41JXMN109187"]
            }
        }

class ChatHistoryListRequest(BaseModel):
    """Request model for vehicle queries"""
    page: int = Field(1, description="Current Page of the inventory")
    page_size: int = Field(10, description="Total vehicle details fetch per page")
    search: Optional[str] = Field(None, description="Search a chat list")

    class Config:
        json_schema_extra = {
            "example": {
                "page": 1,
                "page_size": 10,
                "search": "show me audi"
            }
        }

class ChatHistoryRequest(BaseModel):
    """Request model for vehicle queries"""
    page: int = Field(1, description="Current Page of the pagination for the user conversation")
    page_size: int = Field(10, description="Total item fetch per page")
    conversation_id:str = Field(..., description="Conversation Id used against the session")
    class Config:
        json_schema_extra = {
            "example": {
                "page": 1,
                'page_size': 10,
                'conversation_id': '6852884ce5cab413y9b04742'
            }
        }

class TestInventoryRequest(BaseModel):
    """Request model for vehicle queries"""
    user_id: Optional[int] = Field(None, description="ID of the user")
    conversation_id: Optional[str] = Field(None, description="ID of the conversation")
    lat: float = Field(..., description='Latitude for the User Location')
    long: float = Field(..., description='Longitude for the User Location')
    page: int = Field(1, description="Current Page of the inventory")
    page_size: int = Field(20, description="Total vehicle details fetched per page")
    sort_by: str = Field('price', description="Field to sort by")
    sort_order: str = Field('asc', description="Sort order (asc/desc)")
    filters: Optional[Dict[str, Any]] = Field(None, description="Optional filters for vehicle search")
    addedCarinGarageVins: Optional[List[str]] = Field(None, description="List of VIN numbers to exclude from search results")

    class Config:
        json_schema_extra = {
            "example": {
                "user_id": 1234,
                "conversation_id": "abcde-67890",
                "lat": 34.01,
                "long": -118.41,
                "page": 1,
                "page_size": 20,
                "sort_by": "price",
                "sort_order": "asc",
                "filters": {
                    "make": "Audi",
                    "model": "A4",
                    "year_min": 2023,
                    "year_max": 2023,
                    "price_min": None,
                    "price_max": None,
                    "mileage_max": None,
                    "fuel_type": None,
                    "transmission": None,
                    "body_type": None,
                    "condition": None
                }
            }
        }

class FeedbackRequest(BaseModel):
    """Request model for vehicle queries"""
    message_id: str = Field(..., description="ID of the message")
    feedback_flag: int = Field(None, description="Feedback flag (1 for like, 0 for dislike)")
    feedback_message: Optional[str] = Field(None, description="Feedback message provided by user")

    class Config:
        json_schema_extra = {
            "example": {
                "message_id": "abcde-67890",
                "message_flag": 0,
                "feedback_message": "Didnt like the response. Provide me a better response"
            }
        }

class DeleteMessageRequest(BaseModel):
    """Request model for deleting a message"""
    conversation_id: str = Field(..., description="ID of the conversation")
    class Config:
        json_schema_extra = {
            "example": {
                "conversation_id": "abcde-67890"
            }
        }


class MessageUpdate(BaseModel):
    """Request model for updating a message"""
    message_id: str = Field(..., description="_id represents the message ID")
    content: str = Field(..., description="Updated content of the message")
    is_inventory_fetch: Optional[int] = Field(default=0, description="Flag indicating if inventory was fetched (1 if yes, 0 if no)")
    inventory_vehicles: Optional[List[Dict[str, Any]]] = Field(default=[], description="Vehicle inventory data when available")

    class Config:
        json_schema_extra = {
            "example": {
                "message_id": "abcde-67890",
                "content": "Updated content of the message",
                "is_inventory_fetch": 1,
                "inventory_vehicles": [
                    {
                        "row_id": 7522209,
                        "body_type": "SUV",
                        "exterior_color": "Nh-912px Urban Gra",
                        "fp_dealer_id": 7954,
                        "fuel_type": None,
                        "heading": "2025 Acura",
                        "high_value_features": None,
                        "interior_color": "Nh-912px/Red",
                        "inventory_type": "new",
                        "latitude": 36.050831,
                        "longitude": -115.026314,
                        "make": "Acura",
                        "miles": None,
                        "model": "ADX",
                        "photo_url": "https://images.dealer.com/unavailable_stockphoto.png",
                        "price": 39950,
                        "stock_no": "40908-20",
                        "trim": None,
                        "vin": "3HDSA1H52SM701682",
                        "year": 2025
                    }
                ]
            }
        }