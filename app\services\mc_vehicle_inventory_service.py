from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import asc, desc, select, or_, func, not_
from sqlalchemy.dialects import mssql
from app.db.session import DatabaseSession
from app.models.mc_vehicle_inventory import McVehicleInventory
from app.models.responses import VehicleInventoryItem
from app.config.logger import log_info, log_error
from fastapi import Request
from typing import Dict, Any
import json
import time

class VehicleInventoryService:
    def __init__(self, req: Request):
        try:
            self.req = req
            log_info(self.req, "VehicleInventoryService initialized successfully.")
        except Exception as e:
            log_error(self.req, f"Error initializing VehicleInventoryService: {str(e)}")
            raise

    async def connect(self):
        """Establish async database connection"""
        try:
            connect_start_time = time.time()
            log_info(self.req, "[TIMING] VehicleInventoryService.connect: Starting database connection")

            self.session = await DatabaseSession.get_async_session()

            connect_elapsed = time.time() - connect_start_time
            log_info(self.req, f"[TIMING] VehicleInventoryService.connect: Database connection completed - Elapsed: {connect_elapsed:.3f}s")
        except Exception as e:
            log_error(self.req, f"Error connecting to database: {str(e)}")
            raise

    async def close(self):
        """Close async database connection"""
        try:
            close_start_time = time.time()
            log_info(self.req, "[TIMING] VehicleInventoryService.close: Starting database connection cleanup")

            if hasattr(self, 'session') and self.session:
                await self.session.close()

            close_elapsed = time.time() - close_start_time
            log_info(self.req, f"[TIMING] VehicleInventoryService.close: Database connection cleanup completed - Elapsed: {close_elapsed:.3f}s")
        except Exception as e:
            log_error(self.req, f"Error closing database connection: {str(e)}")
            raise




    async def get_filtered_inventory(self, filters: dict, approved_amount: float, req: Request, page: int = 1, page_size: int = 20, sort_by: str = 'price', sort_order: str = 'asc', dealer_ids: list = [], excluded_vins: list = []) -> Dict[str, Any]:
        try:
            # Start overall timing for inventory filtering
            inventory_start_time = time.time()
            log_info(req, "[TIMING] VehicleInventoryService.get_filtered_inventory: Starting inventory filtering process")
            log_info(req, f"Filtering inventory with filters: {filters} and approved_amount: {approved_amount}")

            if not filters and not approved_amount and not dealer_ids:
                log_info(req, "No filters or approved amount provided or dealers_id found. Returning empty response.")
                return {
                    "vehicles": [],
                    "total_vehicle_count": 0
                }

            # Time query preparation phase
            query_prep_start_time = time.time()
            log_info(req, "[TIMING] VehicleInventoryService.query_preparation: Starting SQL query preparation")

            selected_fields = [
                McVehicleInventory.row_id,
                McVehicleInventory.body_type,
                McVehicleInventory.exterior_color,
                McVehicleInventory.fp_dealer_id,
                McVehicleInventory.fuel_type,
                McVehicleInventory.heading,
                McVehicleInventory.high_value_features,
                McVehicleInventory.interior_color,
                McVehicleInventory.inventory_type,
                McVehicleInventory.latitude,
                McVehicleInventory.longitude,
                McVehicleInventory.make,
                McVehicleInventory.miles,
                McVehicleInventory.model,
                McVehicleInventory.photo_url,
                McVehicleInventory.price,
                McVehicleInventory.stock_no,
                McVehicleInventory.trim,
                McVehicleInventory.vin,
                McVehicleInventory.year,
            ]
            # Build async query using select
            stmt = select(*selected_fields)
            print("filters", filters)

            # Handle case where filters might be a string (defensive programming)
            if isinstance(filters, str):
                try:
                    filters = json.loads(filters)
                except json.JSONDecodeError:
                    log_error(req, f"Invalid filter string format: {filters}")
                    filters = {}

            # Ensure filters is a dict
            if not isinstance(filters, dict):
                filters = {}

            # Time filter application phase
            filter_apply_start_time = time.time()
            log_info(req, "[TIMING] VehicleInventoryService.filter_application: Starting filter criteria application")

            # Make
            if filters.get("make"):
                make = filters["make"]
                if isinstance(make, list) and make:
                    # Use OR conditions with LIKE for each make in the list
                    make_conditions = [McVehicleInventory.make.like(f"%{m}%") for m in make if m.strip()]
                    if make_conditions:
                        stmt = stmt.where(or_(*make_conditions))
                elif isinstance(make, str) and make.strip():
                    stmt = stmt.where(McVehicleInventory.make.like(f"%{make}%"))

            # Model
            if filters.get("model"):
                model = filters["model"]
                if isinstance(model, list) and model:
                    # Use OR conditions with LIKE for each model in the list
                    model_conditions = [McVehicleInventory.model.like(f"%{m}%") for m in model if m.strip()]
                    if model_conditions:
                        stmt = stmt.where(or_(*model_conditions))
                elif isinstance(model, str) and model.strip():
                    stmt = stmt.where(McVehicleInventory.model.like(f"%{model}%"))

            # Year
            if filters.get("year"):
                year = filters["year"]
                if isinstance(year, list) and year:
                    stmt = stmt.where(McVehicleInventory.year.in_([int(y) for y in year if str(y).isdigit()]))
                elif isinstance(year, int):
                    stmt = stmt.where(McVehicleInventory.year == year)

            # Trim
            if filters.get("trim"):
                trim = filters["trim"]
                if isinstance(trim, list) and trim:
                    # Use OR conditions with LIKE for each trim in the list
                    trim_conditions = [McVehicleInventory.trim.like(f"%{t}%") for t in trim if t.strip()]
                    if trim_conditions:
                        stmt = stmt.where(or_(*trim_conditions))
                elif isinstance(trim, str) and trim.strip():
                    stmt = stmt.where(McVehicleInventory.trim.like(f"%{trim}%"))

            # Price Min
            if filters.get("price_min"):
                try:
                    stmt = stmt.where(McVehicleInventory.price >= float(filters["price_min"]))
                except (ValueError, TypeError):
                    log_info(req, "Invalid price_min value. Skipping.")

            # Price Max
            if filters.get("price_min"):
                try:
                    stmt = stmt.where(McVehicleInventory.price <= float(filters["price_max"]))
                except (ValueError, TypeError):
                    log_info(req, "Invalid price_max value. Skipping.")

            # Mileage Min
            if filters.get("mileage_min"):
                try:
                    stmt = stmt.where(McVehicleInventory.miles >= float(filters["mileage_min"]))
                except (ValueError, TypeError):
                    log_info(req, "Invalid mileage_min value. Skipping.")

            # Mileage Max
            if filters.get("mileage_max"):
                try:
                    stmt = stmt.where(McVehicleInventory.miles <= float(filters["mileage_max"]))
                except (ValueError, TypeError):
                    log_info(req, "Invalid mileage_max value. Skipping.")

            # Fuel Type
            if filters.get("fuel_type"):
                fuel = filters["fuel_type"]
                if isinstance(fuel, list) and fuel:
                    stmt = stmt.where(McVehicleInventory.fuel_type.in_([f for f in fuel if f.strip()]))
                elif isinstance(fuel, str) and fuel.strip():
                    stmt = stmt.where(McVehicleInventory.fuel_type == fuel)

            # Inventory Type
            if filters.get("type"):
                inv_type = filters["type"]
                if isinstance(inv_type, list) and inv_type:
                    stmt = stmt.where(McVehicleInventory.inventory_type.in_([t for t in inv_type if t.strip()]))
                elif isinstance(inv_type, str) and inv_type.strip():
                    stmt = stmt.where(McVehicleInventory.inventory_type == inv_type)

            # Exterior Color
            if filters.get("exterior_color"):
                ext_color = filters["exterior_color"]
                if isinstance(ext_color, list) and ext_color:
                    # Use OR conditions with LIKE for each exterior color in the list
                    ext_color_conditions = [McVehicleInventory.exterior_color.like(f"%{c}%") for c in ext_color if c.strip()]
                    if ext_color_conditions:
                        stmt = stmt.where(or_(*ext_color_conditions))
                elif isinstance(ext_color, str) and ext_color.strip():
                    stmt = stmt.where(McVehicleInventory.exterior_color.like(f"%{ext_color}%"))

            # Interior Color
            if filters.get("interior_color"):
                int_color = filters["interior_color"]
                if isinstance(int_color, list) and int_color:
                    # Use OR conditions with LIKE for each interior color in the list
                    int_color_conditions = [McVehicleInventory.interior_color.like(f"%{c}%") for c in int_color if c.strip()]
                    if int_color_conditions:
                        stmt = stmt.where(or_(*int_color_conditions))
                elif isinstance(int_color, str) and int_color.strip():
                    stmt = stmt.where(McVehicleInventory.interior_color.like(f"%{int_color}%"))

            # Exclude vehicles with VINs in the excluded list
            if excluded_vins:
                log_info(req, f"Excluding {len(excluded_vins)} VINs from search results")
                stmt = stmt.where(not_(McVehicleInventory.vin.in_(excluded_vins)))

            stmt = stmt.where(McVehicleInventory.price <= approved_amount)
            stmt = stmt.where(McVehicleInventory.fp_dealer_id.in_(dealer_ids))

            filter_apply_elapsed = time.time() - filter_apply_start_time
            log_info(req, f"[TIMING] VehicleInventoryService.filter_application: Filter criteria application completed - Elapsed: {filter_apply_elapsed:.3f}s")

            # Time total count query (before pagination)
            count_query_start_time = time.time()
            log_info(req, "[TIMING] VehicleInventoryService.count_query: Starting total vehicle count query")

            # Create count query using the same filters but without pagination
            count_stmt = select(func.count(McVehicleInventory.row_id))

            # Apply the same filters to count query
            if filters.get("make"):
                make = filters["make"]
                if isinstance(make, list) and make:
                    make_conditions = [McVehicleInventory.make.like(f"%{m}%") for m in make if m.strip()]
                    if make_conditions:
                        count_stmt = count_stmt.where(or_(*make_conditions))
                elif isinstance(make, str) and make.strip():
                    count_stmt = count_stmt.where(McVehicleInventory.make.like(f"%{make}%"))

            if filters.get("model"):
                model = filters["model"]
                if isinstance(model, list) and model:
                    model_conditions = [McVehicleInventory.model.like(f"%{m}%") for m in model if m.strip()]
                    if model_conditions:
                        count_stmt = count_stmt.where(or_(*model_conditions))
                elif isinstance(model, str) and model.strip():
                    count_stmt = count_stmt.where(McVehicleInventory.model.like(f"%{model}%"))

            if filters.get("year"):
                year = filters["year"]
                if isinstance(year, list) and year:
                    count_stmt = count_stmt.where(McVehicleInventory.year.in_([int(y) for y in year if str(y).isdigit()]))
                elif isinstance(year, int):
                    count_stmt = count_stmt.where(McVehicleInventory.year == year)

            if filters.get("trim"):
                trim = filters["trim"]
                if isinstance(trim, list) and trim:
                    trim_conditions = [McVehicleInventory.trim.like(f"%{t}%") for t in trim if t.strip()]
                    if trim_conditions:
                        count_stmt = count_stmt.where(or_(*trim_conditions))
                elif isinstance(trim, str) and trim.strip():
                    count_stmt = count_stmt.where(McVehicleInventory.trim.like(f"%{trim}%"))

            if filters.get("price_min"):
                try:
                    count_stmt = count_stmt.where(McVehicleInventory.price >= float(filters["price_min"]))
                except (ValueError, TypeError):
                    pass

            if filters.get("price_max"):
                try:
                    count_stmt = count_stmt.where(McVehicleInventory.price <= float(filters["price_max"]))
                except (ValueError, TypeError):
                    pass

            if filters.get("mileage_min"):
                try:
                    count_stmt = count_stmt.where(McVehicleInventory.miles >= float(filters["mileage_min"]))
                except (ValueError, TypeError):
                    pass

            if filters.get("mileage_max"):
                try:
                    count_stmt = count_stmt.where(McVehicleInventory.miles <= float(filters["mileage_max"]))
                except (ValueError, TypeError):
                    pass

            if filters.get("fuel_type"):
                fuel = filters["fuel_type"]
                if isinstance(fuel, list) and fuel:
                    count_stmt = count_stmt.where(McVehicleInventory.fuel_type.in_([f for f in fuel if f.strip()]))
                elif isinstance(fuel, str) and fuel.strip():
                    count_stmt = count_stmt.where(McVehicleInventory.fuel_type == fuel)



            if filters.get("type"):
                inv_type = filters["type"]
                if isinstance(inv_type, list) and inv_type:
                    count_stmt = count_stmt.where(McVehicleInventory.inventory_type.in_([t for t in inv_type if t.strip()]))
                elif isinstance(inv_type, str) and inv_type.strip():
                    count_stmt = count_stmt.where(McVehicleInventory.inventory_type == inv_type)

            # Exterior Color
            if filters.get("exterior_color"):
                ext_color = filters["exterior_color"]
                if isinstance(ext_color, list) and ext_color:
                    ext_color_conditions = [McVehicleInventory.exterior_color.like(f"%{c}%") for c in ext_color if c.strip()]
                    if ext_color_conditions:
                        count_stmt = count_stmt.where(or_(*ext_color_conditions))
                elif isinstance(ext_color, str) and ext_color.strip():
                    count_stmt = count_stmt.where(McVehicleInventory.exterior_color.like(f"%{ext_color}%"))

            # Interior Color
            if filters.get("interior_color"):
                int_color = filters["interior_color"]
                if isinstance(int_color, list) and int_color:
                    int_color_conditions = [McVehicleInventory.interior_color.like(f"%{c}%") for c in int_color if c.strip()]
                    if int_color_conditions:
                        count_stmt = count_stmt.where(or_(*int_color_conditions))
                elif isinstance(int_color, str) and int_color.strip():
                    count_stmt = count_stmt.where(McVehicleInventory.interior_color.like(f"%{int_color}%"))

            # Exclude vehicles with VINs in the excluded list (same as main query)
            if excluded_vins:
                count_stmt = count_stmt.where(not_(McVehicleInventory.vin.in_(excluded_vins)))

            # Apply the same final filters
            count_stmt = count_stmt.where(McVehicleInventory.price <= approved_amount)
            count_stmt = count_stmt.where(McVehicleInventory.fp_dealer_id.in_(dealer_ids))

            # Execute count query
            count_result = await self.session.execute(count_stmt)
            total_vehicle_count = count_result.scalar() or 0

            count_query_elapsed = time.time() - count_query_start_time
            log_info(req, f"[TIMING] VehicleInventoryService.count_query: Total vehicle count query completed - Elapsed: {count_query_elapsed:.3f}s")

            # Time sorting and pagination setup
            sort_setup_start_time = time.time()
            log_info(req, "[TIMING] VehicleInventoryService.sort_pagination_setup: Starting sorting and pagination setup")

            # List of allowed columns for sorting
            allowed_sort_by = {
                "price": McVehicleInventory.price,
                "miles": McVehicleInventory.miles,
                "year": McVehicleInventory.year
            }

            # Default to 'price' if not in allowed list
            sort_by_clean = sort_by
            sort_column = allowed_sort_by.get(sort_by_clean, McVehicleInventory.price)

            # List of allowed sort orders
            allowed_sort_orders = ["asc", "desc"]
            sort_order_clean = sort_order
            valid_sort_order = sort_order_clean if sort_order_clean in allowed_sort_orders else "asc"

            # Apply ordering
            if valid_sort_order == "desc":
                stmt = stmt.order_by(desc(sort_column))
            else:
                stmt = stmt.order_by(asc(sort_column))

            # Apply pagination
            offset = (page - 1) * page_size
            stmt = stmt.offset(offset).limit(page_size)

            sort_setup_elapsed = time.time() - sort_setup_start_time
            log_info(req, f"[TIMING] VehicleInventoryService.sort_pagination_setup: Sorting and pagination setup completed - Elapsed: {sort_setup_elapsed:.3f}s")

            query_prep_elapsed = time.time() - query_prep_start_time
            log_info(req, f"[TIMING] VehicleInventoryService.query_preparation: SQL query preparation completed - Elapsed: {query_prep_elapsed:.3f}s")

            # Time database execution (critical operation)
            db_execute_start_time = time.time()
            log_info(req, "[TIMING] VehicleInventoryService.database_execution: Starting database query execution")

            result = await self.session.execute(stmt)
            rows = result.fetchall()

            db_execute_elapsed = time.time() - db_execute_start_time
            log_info(req, f"[TIMING] VehicleInventoryService.database_execution: Database query execution completed - Elapsed: {db_execute_elapsed:.3f}s")

            # Time result processing
            result_process_start_time = time.time()
            log_info(req, "[TIMING] VehicleInventoryService.result_processing: Starting result data processing")

            processed_results = [
                VehicleInventoryItem(**{col.key: getattr(row, col.key) for col in selected_fields})
                for row in rows
            ]

            result_process_elapsed = time.time() - result_process_start_time
            log_info(req, f"[TIMING] VehicleInventoryService.result_processing: Result data processing completed - Elapsed: {result_process_elapsed:.3f}s")

            # Log overall completion timing
            inventory_elapsed = time.time() - inventory_start_time
            log_info(req, f"[TIMING] VehicleInventoryService.get_filtered_inventory: Complete inventory filtering process finished - Total Elapsed: {inventory_elapsed:.3f}s")
            log_info(req, f"Retrieved {len(rows)} inventory items matching filters, total: {total_vehicle_count}")

            # Return response with meaningful total count key
            return {
                "vehicles": processed_results,
                "total_vehicle_count": total_vehicle_count
            }

        except Exception as db_err:
            log_error(req, f"[get_filtered_inventory] DB query failed: {db_err}")
            raise
